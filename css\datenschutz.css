/* datenschutz.css */
:root {
  --primary-color: #004a99;
  --secondary-color: #003366;
  --light-background: #e0f0ff;
  --font-family: '<PERSON>o', sans-serif;
  --text-color: #333;
  --background-color: #ffffff;
  --link-hover-color: #007bff;
}
/* Main Content Styling */
main {
  max-width: 800px;
  margin: 100px auto 60px; /* Account for fixed header */
  padding: 0 20px;
}

h1 {
  font-size: 2.0em;
  margin-bottom: 20px;
  color: var(--primary-color);
  font-weight: 700;
}

h3 {
  color: var(--primary-color);
  margin-bottom: 30px;
  font-size: 2em;
  font-weight: 600;
}

section {
  margin-bottom: 40px;
}

h2 {
  color: var(--primary-color);
  margin-bottom: 20px;
  font-size: 1.75em;
  font-weight: 600;
}

p,
li,
address {
  line-height: 1.8;
  margin-bottom: 15px;
  color: var(--text-color);
  font-size: 1em;
}

/* List Styling for Better Indentation */
ol, ul {
  padding-left: 30px; /* Increased indentation for better visual hierarchy */
  margin-bottom: 20px;
}

ol li, ul li {
  margin-bottom: 10px; /* Add spacing between list items */
  padding-left: 5px; /* Additional padding for list item content */
}

/* Nested lists */
ol ol, ol ul, ul ol, ul ul {
  margin-top: 10px;
  margin-bottom: 10px;
  padding-left: 25px; /* Indent nested lists further */
}

/* Specific styling for unordered lists */
ul {
  list-style-type: disc; /* Ensure bullet points are visible */
}

ul ul {
  list-style-type: circle; /* Different bullet style for nested lists */
}

ul ul ul {
  list-style-type: square; /* Third level nesting */
}

address {
  font-style: normal;
}

/* Responsive Design */
@media (max-width: 768px) {
  main {
    margin: 100px 10px 60px; /* Adjust margins for smaller screens */
    padding: 0;
  }
}
